/* eslint-disable @typescript-eslint/no-explicit-any */
'use server';
import { WidgetType } from '@/types/common';
import { FormattedHomePageData, HomePageData } from '@/types/home';
import { apiClient } from '@/lib/api-client';
import { ApiError, ApiResponse } from '@/types/api';
import { cookies } from 'next/headers';
import { getCookie } from 'cookies-next/server';

const getFormattedGrid = (widgetsMap: Record<string, any>, widgetID: string) => {
  return {
    id: widgetID,
    type: WidgetType.ImageGrid,
    data: {
      title: widgetsMap[widgetID].heading,
      rows: widgetsMap[widgetID].items_per_row,
      items: widgetsMap[widgetID].items.map((item: any) => ({
        href: item.deeplink,
        rows: widgetsMap[widgetID].items_per_row,
        image: {
          light: item.image_media.media_url.l,
          dark: item.image_media.media_url.d,
          imageHeight: item.height,
          imageWidth: item.width,
        },
        text: item.text,
      })),
    },
  };
};

const formatData = (data: HomePageData, type: 'men' | 'women'): FormattedHomePageData => {
  const baseWidget = `hp_${type}`;
  const productGridCarouselId = type === 'men' ? 'carousel153' : 'carousel217';

  const widgetsMap: Record<string, any> = {};

  data.component_data.forEach((item) => {
    widgetsMap[item.bloc_id] = item;
  });

  const widgetsList = widgetsMap[baseWidget].widgets;
  const productGridWidget = widgetsMap[productGridCarouselId];

  const widgetsToRender: FormattedHomePageData['widgets'] = widgetsList
    .map((widget: any, index: number) => {
      if (widgetsMap[widget].type === 'grid') {
        return getFormattedGrid(widgetsMap, widget);
      }
      if (widgetsMap[widget].type === 'row') {
        return {
          id: widget,
          type: WidgetType.ImageRow,
          data: {
            rows: widgetsMap[widget].items.length,
            title: widgetsMap[widget].heading,
            items: widgetsMap[widget].items.map((item: any) => ({
              href: item.deeplink,
              image: {
                light: item.image_media.media_url.l,
                dark: item.image_media.media_url.d,
                imageHeight: item.height,
                imageWidth: item.width,
              },
            })),
          },
        };
      }
      if (widgetsMap[widget].type === 'banner') {
        let productListWidgetId = '';
        if (widgetsMap[widgetsList[index + 1]].type === 'product_row') {
          productListWidgetId = widgetsMap[widgetsList[index + 1]].id;
        }
        return {
          id: widget,
          type: WidgetType.Banner,
          data: {
            title: widgetsMap[widget].heading,
            productListWidgetId,
            rows: 1,
            items: widgetsMap[widget].items.map((item: any) => ({
              href: item.deeplink,
              image: {
                light: item.image_media.media_url.l,
                dark: item.image_media.media_url.d,
                imageHeight: item.height || 200,
                imageWidth: item.width || 375,
              },
            })),
          },
        };
      }
    })
    .filter((widget: FormattedHomePageData['widgets']) => widget !== undefined);

  const formattedData = {
    tagHeader: {
      light: widgetsMap[baseWidget].top_media.media_url.l,
      dark: widgetsMap[baseWidget].top_media.media_url.d,
    },
    productCarousel: widgetsMap[baseWidget].product_carousel.map((item: any) => ({
      href: item.deeplink,
      image: {
        light: item.image_url.media_url.l,
        dark: item.image_url.media_url.d,
      },
    })),
    productGridCarousel: productGridWidget.items.map((item: any) => {
      const gridId = widgetsMap[baseWidget][item.component.items.v.reference][0];
      const gridData = getFormattedGrid(widgetsMap, gridId);
      return gridData.data;
    }),
    widgets: widgetsToRender,
    searchSuggestions: widgetsMap[baseWidget].search_suggestions.map((row: any[]) =>
      row.map((item) => ({
        name: item.name,
        href: item.deeplink,
      }))
    ),
  } as FormattedHomePageData;

  return formattedData;
};

export const getHomeData = async (gender: 'men' | 'women'): Promise<FormattedHomePageData> => {
  try {
    const decryptedData = await apiClient<HomePageData>({
      path: `/page/home/<USER>/hp_${gender}`,
    });

    if (!decryptedData || !decryptedData.component_data) {
      throw new ApiError(`Invalid ${gender} data structure`, 500);
    }
    const formattedData = formatData(decryptedData, gender);
    const response: FormattedHomePageData = {
      ...formattedData,
      tokens: {
        session_token: decryptedData.tokens.session_token,
        refresh_token: decryptedData.tokens.refresh_token,
      },
      global_state: decryptedData.global_state,
      toast: decryptedData.toast,
      timestamp: decryptedData.timestamp,
    };
    return response;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new Error(`Failed to load ${gender} data. Please try again later.`);
  }
};

export const getInitialHomeData = async (): Promise<ApiResponse> => {
  const preference = await getCookie('X-User-Pref', { cookies });
  const url = preference ? `/page/home_data?qp=${preference}` : '/page/home_data';
  const res = await apiClient({
    path: url,
    method: 'GET',
  });
  return res;
};
