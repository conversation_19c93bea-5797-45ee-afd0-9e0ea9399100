'use client';

import { useSearchParams } from 'next/navigation';
import { useAppConfigStore } from './app-config';
import { setCookie } from 'cookies-next/client';

export function useTagWithUrlSupport() {
  const searchParams = useSearchParams();
  const { updateConfig } = useAppConfigStore();

  // Get tag from URL
  const urlTag = searchParams?.get('shop') as 'man' | 'woman' | 'brands' | null;

  // Wrapper for setTag that also updates URL
  const setTagWithUrl = (tag: 'man' | 'woman' | 'brands') => {
    // Update URL without refetch
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('shop', tag);
    window.history.pushState({ path: newUrl.href }, '', newUrl.href);
    if (tag !== 'brands') {
      updateConfig({ userPreference: tag });
      setCookie('X-User-Pref', tag);
    } else {
      setCookie('X-User-Pref', null);
    }
  };

  return {
    tag: urlTag,
    setTag: setTagWithUrl,
  };
}
