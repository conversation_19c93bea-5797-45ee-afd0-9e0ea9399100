'use client';

import { useEffect, useRef } from 'react';
import { useStatsStore } from '@/store/stats.store';
import { useStatsMutation } from '@/mutations/stats.mutations';

export const useVisibilityTracker = ({ parentId }: { parentId?: string }) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const statsIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { addVisibilityTime, clearStats } = useStatsStore();
  const statsMutation = useStatsMutation();

  const checkVisibility = () => {
    const trackElements = document.querySelectorAll('.track');

    if (parentId) {
      const parentElement = document.getElementById(parentId);
      if (parentElement) {
        const parentRect = parentElement.getBoundingClientRect();

        trackElements.forEach((el) => {
          const rect = el.getBoundingClientRect();
          const isFullyVisible =
            rect.top >= parentRect.top &&
            rect.left >= parentRect.left &&
            rect.bottom <= parentRect.bottom &&
            rect.right <= parentRect.right &&
            rect.width !== 0 &&
            rect.height !== 0;

          if (isFullyVisible) {
            const elementId = el.getAttribute('data-track-id');
            if (elementId) {
              addVisibilityTime(elementId, 1);
            }
          }
        });
      }
    }
  };

  const sendStatsData = async () => {
    try {
      // use the local storage to get the stats
      const stats = localStorage.getItem('stats-storage');
      if (!stats) {
        return;
      }
      const parsedStats = JSON.parse(stats);
      const statsBody = parsedStats.state.stats;
      clearStats();

      if (Object.keys(statsBody).length === 0) {
        return;
      }
      await statsMutation.mutateAsync(statsBody).catch((error) => {
        console.error('Failed to send stats data:', error);
        // adding the failed stats back to the store (HEHE)
        Object.entries(statsBody).forEach(([key, duration]) => {
          addVisibilityTime(key, parseInt((duration as string).toString()));
        });
      });
    } catch (error) {
      console.error('Error in sendStatsData:', error);
    }
  };
  useEffect(() => {
    // only run in production
    if (process.env.NEXT_PUBLIC_ENV === 'development') return;
    checkVisibility();
    intervalRef.current = setInterval(checkVisibility, 500);
    statsIntervalRef.current = setInterval(sendStatsData, 30000);
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (statsIntervalRef.current) {
        clearInterval(statsIntervalRef.current);
      }
    };
  }, []);

  const refreshTracking = () => {
    checkVisibility();
  };

  return { refreshTracking, checkVisibility };
};
