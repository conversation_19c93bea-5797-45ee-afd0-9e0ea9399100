'use client';
import { getCookie } from 'cookies-next/client';

export const useGetHeaders = (): Record<string, string> => {
  const deviceId = getCookie('X-Device-Id');
  const sessionToken = getCookie('x-session-token');
  const xPresentLat = getCookie('X-Present-Lat');
  const xPresentLong = getCookie('X-Present-Long');
  const addressId = getCookie('X-Address-Id');
  const currentTime = new Date().toISOString();

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'X-Device-Id': deviceId || '',
    'X-Current-Time': currentTime,
    operatingSystem: 'web',
    'Ngrok-Skip-Browser-Warning': 'true',
  };

  if (sessionToken) {
    headers['Authorization'] = `Bearer ${sessionToken}`;
  }

  if (xPresentLat && xPresentLong) {
    headers['X-Present-Lat'] = xPresentLat;
    headers['X-Present-Long'] = xPresentLong;
  }

  if (addressId) {
    headers['X-Address-Id'] = addressId;
  }
  return headers;
};
