import { getQueryClient } from '@/get-query-client';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import { getInitialTrendsData, getManData, getWomanData } from '@/actions/trends.actions';
import TrendsTabs from '@/components/trends/trends-tabs';
import { getMetadata } from '@/utils/helper';

export const metadata: Metadata = getMetadata(
  'KNOT',
  'Get fashion in 60-mins. Try before you buy. No waits or regrets, just fire fits!',
  'https://ik.imagekit.io/slickapp/droplet/tr:dpr-2,f-webp:/app_images/1200X630.png?ik-t=9999999999&ik-s=2e4734befc0f9546f1095894dd7bf8f7b9e3e464'
);

// Define the props type for the Trends page
interface TrendsProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function Trends({ searchParams }: TrendsProps) {
  const params = await searchParams;
  const shopParam = params.shop as string | undefined;
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: ['pages', 'trends', 'initial'],
    queryFn: async () => await getInitialTrendsData(),
  });
  await queryClient.prefetchQuery({
    queryKey: ['pages', 'trends', 'man'],
    queryFn: async () => await getManData(),
  });
  await queryClient.prefetchQuery({
    queryKey: ['pages', 'trends', 'woman'],
    queryFn: async () => await getWomanData(),
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <TrendsTabs defaultTab={shopParam} />
    </HydrationBoundary>
  );
}
