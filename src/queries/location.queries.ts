'use client';

import {
  SearchLocationResponse,
  getInitialMapData,
  updateDeliveryLocation,
  AddressByIdResponse,
} from '@/actions/location.actions';
import { useDebounce } from 'use-debounce';
import { useApiQuery } from '@/hooks/use-api-query';
import { useApi } from '@/hooks/use-api';
import { ApiError, ApiResponse } from '@/types/api';
import { useGlobalDataStore } from '@/store/global-data.store';
import { useAppConfigStore } from '@/store/app-config';

export const GET_LOCATION_QUERY_KEY = 'location';

export const GET_DELIVERY_STATUS_QUERY_KEY = 'deliveryStatus';

type DeliveryStatusResponse = {
  delivery_time: string;
  status: string;
  address: string;
} & ApiResponse;

type AddressDataResponse = {
  component_data: Array<{
    bloc_id: string;
    [key: string]: unknown;
  }>;
  location?: {
    current_edit_address_bloc_id: string;
  };
} & ApiResponse;

/*
  Gets the address and delivery time of the given coordinates.
  Used in the "Update Delivery Location" Functionality.
*/
export const useGetDeliveryStatus = (
  params: {
    latitude?: number;
    longitude?: number;
  },
  enabled: boolean = true
) => {
  const { apiClient } = useApi();

  return useApiQuery<DeliveryStatusResponse, ApiError>({
    queryKey: [GET_DELIVERY_STATUS_QUERY_KEY, params.latitude, params.longitude],
    queryFn: async () => {
      const queryParams = new URLSearchParams({
        lat: (params.latitude ?? 0).toString(),
        long: (params.longitude ?? 0).toString(),
      });

      const response = await apiClient<DeliveryStatusResponse>({
        path: `/user_address/check_pincode?${queryParams}`,
        method: 'GET',
      });

      return response;
    },
    staleTime: 1000 * 60 * 5,
    retry: (failureCount, error) => {
      if (error instanceof ApiError && error.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
    enabled: enabled && (!!params.latitude || !!params.longitude),
  });
};

/*
  Debounces the getDeliveryStatus query.
  Runs when the user is dragging the map to update the delivery location.
*/
export const useDebouncedGetDeliveryStatus = (
  params: {
    latitude?: number;
    longitude?: number;
  },
  delay: number = 3000,
  options: {
    enabled?: boolean;
  } = { enabled: true }
) => {
  const [debouncedParams] = useDebounce(params, delay);
  const queryResult = useGetDeliveryStatus(
    debouncedParams,
    options.enabled &&
      typeof debouncedParams?.latitude === 'number' &&
      typeof debouncedParams?.longitude === 'number'
  );

  return queryResult;
};

export const SEARCH_LOCATION_QUERY_KEY = 'searchLocation';

export const useSearchLocation = (
  params: {
    query: string;
  },
  enabled: boolean = true
) => {
  const { apiClient } = useApi();

  return useApiQuery<SearchLocationResponse>({
    queryKey: [SEARCH_LOCATION_QUERY_KEY, params.query],
    queryFn: async () => {
      const queryParams = new URLSearchParams({
        input_text: params.query,
        session_token: '6b752a98-7088-4432-b4de-9a96725c914a',
      });

      const response = await apiClient<SearchLocationResponse>({
        path: `/user_address/autocomplete?${queryParams}`,
        method: 'GET',
      });

      return response;
    },
    staleTime: 1000 * 60 * 5,
    retry: (failureCount, error) => {
      if (error instanceof ApiError && error.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
    enabled: enabled && !!params.query,
  });
};

export const GET_ADDRESS_BY_ID_QUERY_KEY = 'addressById';

export const useGetAddressById = (
  params: {
    addressId: string;
  },
  enabled: boolean = true
) => {
  const { apiClient } = useApi();

  return useApiQuery<AddressByIdResponse, ApiError>({
    queryKey: ['addressById', params.addressId],
    queryFn: async () => {
      const response = await apiClient<AddressDataResponse>({
        path: `/user_address/loc_page_data?address_id=${params.addressId}`,
        method: 'GET',
      });

      try {
        const componentData = response.component_data;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const widgetMap: Record<string, any> = {};

        componentData.map((component) => {
          widgetMap[component.bloc_id] = component;
        });

        const locationData = widgetMap['location'] as { current_edit_address_bloc_id: string };
        const addressData = widgetMap[locationData.current_edit_address_bloc_id];

        return {
          ...addressData,
          global_state: response.global_state,
          toast: response.toast,
          tokens: response.tokens,
        } as AddressByIdResponse;
      } catch (error) {
        console.error('Failed to get address by id:', error);
        throw new Error('Failed to get address');
      }
    },
    staleTime: 1000 * 60 * 5,
    retry: (failureCount, error) => {
      if (error instanceof ApiError && error.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
    enabled: enabled && !!params.addressId,
  });
};

export const GET_INITIAL_MAP_DATA_QUERY_KEY = 'initialMapData';

export const useGetInitialMapData = (params: { addressId?: string }) => {
  const key = params.addressId
    ? [GET_INITIAL_MAP_DATA_QUERY_KEY, params.addressId]
    : [GET_INITIAL_MAP_DATA_QUERY_KEY];
  return useApiQuery<Awaited<ReturnType<typeof getInitialMapData>>, ApiError>({
    queryKey: key,
    queryFn: async () => {
      const data = await getInitialMapData({
        address_id: params.addressId,
      });
      return data;
    },
    staleTime: 1000 * 60 * 5,
    retry: (failureCount, error) => {
      if (error instanceof ApiError && error.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
  });
};

export const GET_INITIAL_LOCATION_DATA_QUERY_KEY = 'initialLocationData';

export const useGetInitialLocationData = () => {
  const { updateConfig } = useAppConfigStore();
  const { globalData, isHydrated } = useGlobalDataStore();
  const currentAddress = globalData.current_address;
  return useApiQuery<Awaited<ReturnType<typeof updateDeliveryLocation>>, ApiError>({
    queryKey: [GET_INITIAL_LOCATION_DATA_QUERY_KEY],
    queryFn: async () => {
      const data = await updateDeliveryLocation({});
      return data;
    },
    staleTime: 1000 * 60 * 5,
    enabled: isHydrated && !currentAddress?.id,
    onSuccess: () => {
      updateConfig({ locationAlreadySet: false });
    },
  });
};
