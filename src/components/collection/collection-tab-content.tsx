'use client';

import React from 'react';
import { useBrandsData, useManData, useWomanData } from '@/queries/use-collection-data';
import { Skeleton } from '@/components/ui/skeleton';

import Collection, { CollectionData } from './collection';

interface TabContentProps {
  type: 'man' | 'woman' | 'brands';
}

const TabContent = React.memo(function TabContent({ type }: TabContentProps) {
  const manData = useManData();
  const womanData = useWomanData();
  const brandsData = useBrandsData();

  // Select the appropriate data based on type
  const { data, isLoading } = type === 'brands' ? brandsData : type === 'man' ? manData : womanData;

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return <div className="w-full">{data && <ContentDisplay data={data} type={type} />}</div>;
});

export default TabContent;

function LoadingSkeleton() {
  return (
    <div className="w-full p-4">
      <Skeleton className="h-8 w-48 mb-6" />
      <div className="grid grid-cols-2 gap-4">
        {Array(4)
          .fill(0)
          .map((_, i) => (
            <Skeleton key={i} className="h-40 w-full rounded-md" />
          ))}
      </div>
    </div>
  );
}

function ContentDisplay({
  data,
  type,
}: {
  data: CollectionData[];
  type: 'man' | 'woman' | 'brands';
}) {
  return (
    <div className="w-full">
      <div>
        <div className="h-[92dvh] pt-12">
          <Collection type={type == 'brands' ? 'search' : 'filter'} collections={data} />
        </div>
      </div>
    </div>
  );
}
