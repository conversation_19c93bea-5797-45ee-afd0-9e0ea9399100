'use client';

import * as Tabs from '@radix-ui/react-tabs';
import CollectionTagButton from './collection-tag-button';
import TabContent from './collection-tab-content';
import CollectionNavbar from './collection-navbar';
import { useInitialCollectionData } from '@/queries/use-collection-data';

export default function CollectionTabs({ defaultTab }: { defaultTab: string | undefined }) {
  const { data } = useInitialCollectionData();
  const defaultTag = data?.global_state?.default_tag;

  return (
    <>
      <CollectionNavbar />
      <Tabs.Root defaultValue={defaultTab ?? defaultTag} className="w-full">
        <Tabs.List>
          <div className="flex w-full pl-4 mb-16 justify-center gap-12">
            <CollectionTagButton tag="man" />
            <CollectionTagButton tag="woman" />
            <CollectionTagButton tag="brands" />
          </div>
        </Tabs.List>

        <div className="relative">
          <Tabs.Content value="man" className="w-full data-[state=inactive]:hidden" forceMount>
            <TabContent type="man" />
          </Tabs.Content>
          <Tabs.Content value="woman" className="w-full data-[state=inactive]:hidden" forceMount>
            <TabContent type="woman" />
          </Tabs.Content>
          <Tabs.Content value="brands" className="w-full data-[state=inactive]:hidden" forceMount>
            <TabContent type="brands" />
          </Tabs.Content>
        </div>
      </Tabs.Root>
    </>
  );
}
