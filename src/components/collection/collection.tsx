'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { useDebouncedCallback } from 'use-debounce';

import { CollectionData, CollectionOption } from '@/types/collection';
import { ImageBoxResponsive } from '../common/image-box';
import { Input } from '../ui/input';
import useAmplitudeContext from '@/hooks/use-analytics';
import { SearchIcon } from 'lucide-react';

interface CollectionProps {
  reference?: string;
  collections: CollectionData[];
  isLoading?: boolean;
  hasError?: boolean;
  onRetry?: () => void;
}

// Utility functions
const fuzzySearch = (query: string, items: CollectionOption[]): CollectionOption[] => {
  if (!query.trim()) return items;

  const lowerQuery = query.toLowerCase();
  return items.filter((item) => item.title.toLowerCase().includes(lowerQuery));
};

// Filter Collection Component
const FilterCollection: React.FC<CollectionProps> = ({ collections }) => {
  const [indicatorIndex, setIndicatorIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isProgrammaticScroll, setIsProgrammaticScroll] = useState(false);

  const mainScrollRef = useRef<HTMLDivElement>(null);
  const sideScrollRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const sidebarItemRefs = useRef<(HTMLButtonElement | null)[]>([]);

  const scrollToIndex = useCallback(
    async (targetIndex: number) => {
      if (isAnimating || !mainScrollRef.current) return;

      setIsAnimating(true);
      setIsProgrammaticScroll(true);
      setIndicatorIndex(targetIndex);

      const targetElement = itemRefs.current[targetIndex];
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }

      // Reset flags after animation completes
      setTimeout(() => {
        setIsAnimating(false);
        setIsProgrammaticScroll(false);
      }, 800); // Increased timeout to ensure smooth scroll completes
    },
    [isAnimating]
  );

  // Auto-scroll sidebar when selected item changes
  const scrollSidebarToIndex = useCallback((index: number) => {
    if (!sideScrollRef.current || !sidebarItemRefs.current[index]) return;

    const sidebarContainer = sideScrollRef.current;
    const targetSidebarItem = sidebarItemRefs.current[index];

    if (targetSidebarItem) {
      const containerRect = sidebarContainer.getBoundingClientRect();
      const itemRect = targetSidebarItem.getBoundingClientRect();

      // Check if item is out of view
      const isAboveView = itemRect.top < containerRect.top;
      const isBelowView = itemRect.bottom > containerRect.bottom;

      if (isAboveView || isBelowView) {
        targetSidebarItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center', // Center the item in the sidebar view
        });
      }
    }
  }, []);

  const handleScroll = useCallback(() => {
    // Skip scroll handling during programmatic scrolling
    if (isAnimating || isProgrammaticScroll || !mainScrollRef.current) return;

    const containerRect = mainScrollRef.current.getBoundingClientRect();

    // Find the first section whose heading is at or above the top of the container
    // and ensure no other section is partially visible above it
    let newIndex = 0;

    for (let index = 0; index < itemRefs.current.length; index++) {
      const currentRef = itemRefs.current[index];
      if (!currentRef) continue;

      const currentRect = currentRef.getBoundingClientRect();

      // Check if current section's top is at or above the container top
      if (currentRect.top <= containerRect.top + 10) {
        // 10px tolerance for precision

        // Check if any section above this one is partially visible
        let hasVisibleSectionAbove = false;

        for (let prevIndex = 0; prevIndex < index; prevIndex++) {
          const prevRef = itemRefs.current[prevIndex];
          if (!prevRef) continue;

          const prevRect = prevRef.getBoundingClientRect();

          // If any part of a previous section is visible in the container
          if (prevRect.bottom > containerRect.top && prevRect.top < containerRect.bottom) {
            hasVisibleSectionAbove = true;
            break;
          }
        }

        // If no section above is visible, this should be the selected section
        if (!hasVisibleSectionAbove) {
          newIndex = index;
        }
      } else {
        // If current section's top is below container top,
        // and we haven't found a valid section yet, this should be selected
        if (newIndex === 0 || currentRect.top > containerRect.top) {
          break;
        }
      }
    }

    if (newIndex !== indicatorIndex) {
      setIndicatorIndex(newIndex);
      // Auto-scroll sidebar when selection changes
      scrollSidebarToIndex(newIndex);
    }
  }, [indicatorIndex, isAnimating, isProgrammaticScroll, scrollSidebarToIndex]);

  useEffect(() => {
    const scrollElement = mainScrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll);
      return () => scrollElement.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  if (!collections?.length) return null;

  return (
    <div className="flex gap-4 h-full">
      {/* Sidebar */}
      <div
        ref={sideScrollRef}
        className="flex-shrink-0 overflow-y-auto relative"
        style={{
          borderTopRightRadius: 12,
          borderBottomRightRadius: 12,
          padding: 16,
          backgroundColor: 'var(--surface-secondary)',
        }}
      >
        <div className="flex flex-col gap-8 py-3">
          {collections.map((collection, index) => (
            <button
              key={index}
              ref={(el) => {
                sidebarItemRefs.current[index] = el;
              }}
              onClick={() => scrollToIndex(index)}
              className="flex items-start gap-2 p-2 rounded transition-colors relative"
            >
              {/* Active indicator line */}
              {indicatorIndex === index && (
                <div
                  className="absolute left-0 top-0 bottom-0 w-3.5 rounded-r-lg max-h-60 mt-10"
                  style={{
                    left: '-16px', // Offset by the padding to reach the absolute edge
                    backgroundColor: 'var(--content-primary)',
                  }}
                />
              )}
              <div className="flex flex-col items-center gap-1">
                {/* Fixed container size for consistent sidebar images */}
                <div className="overflow-hidden rounded flex items-center justify-center ">
                  {indicatorIndex === index && collection.selected_image_url ? (
                    <ImageBoxResponsive
                      width={74}
                      height={77}
                      imageUrl={collection.selected_image_url.media_url.l}
                      imageUrlDark={collection.selected_image_url.media_url.d}
                      alt={collection.title}
                      className="w-74 h-77 object-cover"
                    />
                  ) : (
                    <ImageBoxResponsive
                      width={74}
                      height={77}
                      imageUrl={collection.image_url.media_url.l}
                      imageUrlDark={collection.image_url.media_url.d}
                      alt={collection.title}
                      className="w-74 h-77 object-cover"
                    />
                  )}
                </div>

                <span className="text-xs text-center font-bold mt-6 text-content-surface-primary max-w-[74px]">
                  {collection.title}
                </span>
              </div>
            </button>
          ))}

          {/* Add sufficient bottom padding to sidebar to allow last item to be scrolled into view */}
          <div style={{ height: '13dvh' }}></div>
        </div>
      </div>

      {/* Main Content */}
      <div
        ref={mainScrollRef}
        className="flex-1 overflow-y-auto rounded-md bg-surface-secondary mx-12 px-12 py-8 space-y-32"
      >
        {collections.map((collection, collectionIndex) => (
          <div
            key={collectionIndex}
            ref={(el) => {
              itemRefs.current[collectionIndex] = el;
            }}
          >
            <h3 className="font-bold pt-2 mb-8 text-content-surface-primary text-md">
              {collection.title}
            </h3>

            <div className="grid grid-cols-2 gap-12">
              {collection.options.map((option, optionIndex) => (
                <div key={optionIndex} className="flex flex-col items-center">
                  <div className="rounded-xs overflow-hidden flex items-center justify-center">
                    <ImageBoxResponsive
                      width={104}
                      height={112}
                      imageUrl={option.image_url.media_url.l}
                      imageUrlDark={option.image_url.media_url.d}
                      href={option.deeplink}
                      alt={option.title}
                      className="w-104 h-112"
                    />
                  </div>
                  <span className="text-xs text-center mt-6 font-semibold text-content-surface-primary max-w-104">
                    {option.title}
                  </span>
                </div>
              ))}

              {collection.see_all_deeplink && (
                <div className="flex flex-col items-center min-w-104 min-h-112">
                  <Link href={collection.see_all_deeplink}>
                    <div
                      className="flex flex-col  items-center justify-center min-w-104 min-h-112 cursor-pointer mb-2"
                      style={{
                        backgroundColor: 'var(--surface-tertiary)',
                        borderRadius: 12,
                      }}
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        className="transform scale-x-[-1]"
                      >
                        <path
                          d="M15 18L9 12L15 6"
                          stroke={'var(--content-primary)'}
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <span className="text-sm mt-1 text-content-surface-primary font-bold">
                        See All
                      </span>
                    </div>
                  </Link>
                  <span className="text-xs text-center line-clamp-1 text-content-surface-primary max-w-[104px] opacity-0"></span>
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Add sufficient bottom padding to allow last section to scroll to top */}
        <div style={{ height: '30dvh' }}></div>
      </div>
    </div>
  );
};

const SearchCollection: React.FC<
  CollectionProps & {
    searchDebounceDuration?: number;
    onSearch?: (query: string) => void;
  }
> = ({ collections, searchDebounceDuration = 300, onSearch }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredBrands, setFilteredBrands] = useState<CollectionOption[]>([]);

  const allBrands = React.useMemo(() => {
    return collections?.flatMap((collection) => collection.options) || [];
  }, [collections]);

  const debouncedSearchWithDelay = useDebouncedCallback((query: string) => {
    const filtered = fuzzySearch(query, allBrands);
    setFilteredBrands(filtered);
    onSearch?.(query);
  }, searchDebounceDuration);

  useEffect(() => {
    setFilteredBrands(allBrands);
  }, [allBrands]);

  useEffect(() => {
    debouncedSearchWithDelay(searchQuery);
  }, [searchQuery, debouncedSearchWithDelay]);

  const { trackAmplitudeEvent } = useAmplitudeContext();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    trackAmplitudeEvent('Search Collection', {
      search_query: e.target.value,
    });

    setSearchQuery(e.target.value);
  };

  if (!collections?.length) return null;

  return (
    <div className="flex flex-col gap-4 h-full px-12 justify-center">
      <div className="flex-shrink-0">
        <Input
          className="w-full bg-surface-secondary px-12"
          placeholder="Search for 'Souled Store'"
          value={searchQuery}
          onChange={handleSearchChange}
          icon={<SearchIcon className="h-24 w-24" />}
        />
      </div>

      {/* Results */}
      <div className="flex-1 overflow-y-auto mt-16 justify-center">
        <div className="grid grid-cols-4 gap-12">
          {filteredBrands.map((brand, index) => (
            <div key={index} className="flex flex-col items-center">
              {/* Fixed container size for consistent search images too */}
              <div className="overflow-hidden rounded flex items-center justify-center  mb-2">
                <ImageBoxResponsive
                  width={77}
                  height={77}
                  imageUrl={brand.image_url.media_url.l}
                  imageUrlDark={brand.image_url.media_url.d}
                  href={brand.deeplink}
                  alt={brand.title}
                  className="w-77 h-77 object-cover"
                />
              </div>
              <span className="text-xs text-center mt-4">{brand.title}</span>
            </div>
          ))}
        </div>

        <div style={{ height: '20dvh' }}></div>

        {filteredBrands.length === 0 && searchQuery && (
          <div className="text-center text-gray-500 py-8">
            No brands found for &quot;{searchQuery}&quot;
          </div>
        )}
      </div>
    </div>
  );
};

const Collection: React.FC<{
  type: 'filter' | 'search';
  reference?: string;
  collections: CollectionData[];
  isLoading?: boolean;
  hasError?: boolean;
  onRetry?: () => void;
  searchDebounceDuration?: number;
  onSearch?: (query: string) => void;
}> = ({ type, collections, isLoading, hasError, onRetry, searchDebounceDuration, onSearch }) => {
  if (type === 'search') {
    return (
      <SearchCollection
        collections={collections}
        isLoading={isLoading}
        hasError={hasError}
        onRetry={onRetry}
        searchDebounceDuration={searchDebounceDuration}
        onSearch={onSearch}
      />
    );
  }

  return (
    <FilterCollection
      collections={collections}
      isLoading={isLoading}
      hasError={hasError}
      onRetry={onRetry}
    />
  );
};

export default Collection;
export type { CollectionData, CollectionOption };
