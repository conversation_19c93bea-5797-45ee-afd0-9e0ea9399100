'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ExitConfirmationProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export default function ExitConfirmation({ open, onConfirm, onCancel }: ExitConfirmationProps) {
  return (
    <Dialog open={open} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="w-344 bg-surface-secondary-b p-16">
        <DialogHeader className="mb-12">
          <DialogTitle className="text-center text-md">Are you sure you want to exit?</DialogTitle>
          <DialogDescription className="text-center">Your progress will be lost.</DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-16 justify-center flex flex-row">
          <Button
            className="flex-1 h-44 cursor-pointer bg-surface-tertiary hover:bg-surface-tertiary text-[calc(var(--scale)*16)]"
            onClick={onConfirm}
          >
            Yes
          </Button>
          <Button
            className="flex-1 h-44 bg-[var(--theme-red-600)] hover:bg-[var(--theme-red-600)] cursor-pointer text-[calc(var(--scale)*16)]"
            onClick={onCancel}
          >
            No
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
