'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Sheet, SheetContent, SheetTitle } from '../ui/sheet';
import WelcomeForm from './welcome-form';
import ExitConfirmation from './exit-confirmation';
import { useGlobalActionsStore } from '@/store/global-actions.store';
import { useUpdateUser } from '@/mutations/user.mutations';

export default function WelcomeSheet() {
  const { welcomeSheet } = useGlobalActionsStore();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const router = useRouter();
  const updateUserMutation = useUpdateUser();

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Check if any form fields have been filled
      const nameInput: HTMLInputElement | null = document.querySelector('[name="fullName"]');
      const dobInput: HTMLInputElement | null = document.querySelector('[name="dateOfBirth"]');
      const genderButtons = document.querySelectorAll('[data-gender-selected="true"]');
      const referralInput: HTMLInputElement | null =
        document.querySelector('[name="referralCode"]');

      const hasFormData =
        (nameInput?.value && nameInput.value.trim() !== '') ||
        (dobInput?.value && dobInput.value.trim() !== '') ||
        genderButtons.length > 0 ||
        (referralInput?.value && referralInput.value.trim() !== '');

      if (hasFormData) {
        setShowConfirmation(true);
        return;
      }
      updateUserMutation.mutateAsync({
        name: undefined,
        gender: undefined,
        dateOfBirth: undefined,
        referralCode: undefined,
      });

      welcomeSheet.hide();
    }
  };

  const handleConfirmClose = async () => {
    setShowConfirmation(false);

    try {
      // Make update call with empty values
      await updateUserMutation.mutateAsync({
        name: undefined,
        gender: undefined,
        dateOfBirth: undefined,
        referralCode: undefined,
      });
      router.push('/');
    } catch (error) {
      console.error('Failed to update user:', error);
      // Still hide the sheet even if update fails
      welcomeSheet.hide();
    }
  };

  const handleCancelClose = () => {
    setShowConfirmation(false);
  };

  return (
    <>
      <Sheet open={welcomeSheet.isOpen} onOpenChange={handleOpenChange}>
        <SheetTitle className="sr-only">WELCOME TO KNOT</SheetTitle>

        <SheetContent
          className="px-16"
          aria-describedby="Complete your profile to get started with Knot"
        >
          <div className="flex flex-col gap-8 mt-10">
            <WelcomeForm />
          </div>
        </SheetContent>
      </Sheet>

      <ExitConfirmation
        open={showConfirmation}
        onConfirm={handleConfirmClose}
        onCancel={handleCancelClose}
      />
    </>
  );
}
