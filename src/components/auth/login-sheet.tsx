'use client';

import { useState } from 'react';
import { Sheet, SheetContent, SheetTitle } from '../ui/sheet';
import AuthForm from './auth-form';
import ExitConfirmation from './exit-confirmation';
import { useGlobalActionsStore } from '@/store/global-actions.store';

export default function LoginSheet() {
  const { loginSheet } = useGlobalActionsStore();
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Check if user is on OTP step (step 1)
      const authForm = document.querySelector('[data-current-step]');
      const currentStep = authForm?.getAttribute('data-current-step');

      if (currentStep === '0') {
        const phoneNumber: HTMLInputElement | null = document.querySelector('[name="phone"]');
        if (phoneNumber?.value) {
          setShowConfirmation(true);
          return;
        }
      }

      if (currentStep === '1') {
        setShowConfirmation(true);
        return;
      }

      loginSheet.hide();
    }
  };

  const handleConfirmClose = () => {
    setShowConfirmation(false);
    loginSheet.hide();
  };

  const handleCancelClose = () => {
    setShowConfirmation(false);
  };

  return (
    <>
      <Sheet open={loginSheet.isVisible} onOpenChange={handleOpenChange}>
        <SheetTitle className="sr-only">Login to Knot</SheetTitle>
        <SheetContent
          className="px-16"
          aria-describedby="Enter your phone number to login or signup to Knot"
        >
          <div className="flex flex-col gap-8 mt-10">
            <AuthForm />
          </div>
        </SheetContent>
      </Sheet>

      <ExitConfirmation
        open={showConfirmation}
        onConfirm={handleConfirmClose}
        onCancel={handleCancelClose}
      />
    </>
  );
}
