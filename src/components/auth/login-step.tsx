import { Input } from '@/components/ui/input';
import { FormField, FormControl, FormItem } from '@/components/ui/form';
import Link from 'next/link';
import { UseFormReturn } from 'react-hook-form';
import { AuthFormValues } from './auth-form';
import { useSendOTP } from '@/mutations/auth.mutations';
import { toast } from '@/components/ui/sonner';
import { useGlobalActionsStore } from '@/store/global-actions.store';

interface LoginStepProps {
  form: UseFormReturn<AuthFormValues>;
  setCurrentStep: (step: number) => void;
}

export default function LoginStep({ form, setCurrentStep }: LoginStepProps) {
  const { loader } = useGlobalActionsStore();
  const sendOTPMutation = useSendOTP();
  const isLoading = loader.isVisible || sendOTPMutation.isPending;

  const handleContinue = async () => {
    const phoneNumber = form.getValues('phone');
    try {
      const success = await sendOTPMutation.mutateAsync({ phoneNumber });
      if (success) {
        setCurrentStep(1);
      } else {
        toast({
          title: 'Failed to send OTP',
          type: 'error',
        });
      }
    } catch {
      toast({
        title: 'Failed to send OTP',
        type: 'error',
      });
    }
  };

  return (
    <div className="flex flex-col h-full gap-14">
      <p className="font-bold text-lg">Login/Signup</p>
      <div className="gap-4 flex flex-col">
        <p className="text-sm font-medium mb-12">Phone Number</p>
        <div className="flex items-start gap-8">
          <div className="flex items-center justify-center bg-surface-secondary rounded-md size-48">
            <p className="text-sm font-medium">+91</p>
          </div>
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <Input
                    className="w-full"
                    type="tel"
                    placeholder="Phone Number"
                    pattern="[0-9]*"
                    maxLength={10}
                    {...field}
                    onChange={(e) => {
                      const value = e.target.value;
                      const numericValue = value.replace(/[^0-9]/g, '');
                      const truncatedValue = numericValue.slice(0, 10);
                      field.onChange(truncatedValue);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        if (!form.formState.errors.phone && !isLoading && form.getValues('phone')) {
                          handleContinue();
                        }
                      }
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <p className="text-xs text-content-secondary">
          A verification code will be sent to this phone number
        </p>
      </div>
      <div className="flex flex-col py-12 gap-8">
        <p className="text-xs">
          By clicking, I accept the{' '}
          <Link href="/tnc" className="font-semibold">
            Terms & Conditions
          </Link>{' '}
          &{' '}
          <Link href="/pnp" className="font-semibold">
            Privacy Policy
          </Link>
        </p>
        <button
          type="button"
          className="bg-content-theme rounded-sm font-bold w-full disabled:bg-surface-dead-1 transition-colors duration-300 flex items-center justify-center h-48 text-md"
          onClick={handleContinue}
          disabled={!!form.formState.errors.phone || isLoading || !form.getValues('phone')}
        >
          Continue
        </button>
      </div>
    </div>
  );
}
