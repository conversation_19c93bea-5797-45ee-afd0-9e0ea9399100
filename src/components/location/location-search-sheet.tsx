import { useSearchLocation } from '@/queries/location.queries';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>it<PERSON> } from '../ui/sheet';
import { Input } from '../ui/input';
import { ChevronRight, SearchIcon } from 'lucide-react';
import { useState } from 'react';
import LocationCurrentIcon from '../icons/location-current';
import { useDebouncedCallback } from 'use-debounce';
import Loader from '../common/loader';
import LocationEnabled from '../icons/location-enabled';
import { useGetLocationByPlaceId } from '@/mutations/location.mutations';
import { useCurrentLocation } from '@/store/location.store';
import LocationNotEnabledDialogue from './location-not-enabled-dialogue';
import { useGlobalDataStore } from '@/store/global-data.store';
import SavedAddressCard from './saved-address-card';
import { useGlobalActionsStore } from '@/store/global-actions.store';

export default function LocationSearchSheet({
  isO<PERSON>,
  onOpenChange,
  setCords,
  onLocationUpdate,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  setCords: (cords: { latitude: number; longitude: number }) => void;
  onLocationUpdate?: () => void;
}) {
  const [search, setSearch] = useState('');
  const { data: searchResults, isLoading } = useSearchLocation({ query: search });
  const { mutateAsync: getLocationByPlaceId } = useGetLocationByPlaceId();
  const debounced = useDebouncedCallback((value: string) => {
    setSearch(value);
  }, 300);
  const { getCurrentPosition, permissionState } = useCurrentLocation();
  const { globalData } = useGlobalDataStore();
  const { locationNotEnabledDialogue } = useGlobalActionsStore();

  return (
    <Sheet
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          setSearch('');
        }
        onOpenChange(open);
      }}
    >
      <SheetContent className="h-540 flex flex-col max-h-540 z-75">
        <SheetHeader>
          <SheetTitle className="text-center text-md py-8">Select Location</SheetTitle>
        </SheetHeader>
        <div className="flex flex-col flex-1 gap-12 px-16 py-12 ">
          <Input
            className="w-full bg-[#4A4A4A] px-10"
            placeholder="Search for location"
            onChange={(e) => {
              debounced(e.target.value);
            }}
            icon={<SearchIcon className="size-20" />}
          />
          {!searchResults && !isLoading && (
            <div>
              <div
                className="flex w-full px-12 py-14 rounded-md bg-surface-secondary size-48 justify-between items-center cursor-pointer"
                onClick={() => {
                  if (permissionState.status === 'denied' || permissionState.status === 'prompt') {
                    locationNotEnabledDialogue.show();
                  } else {
                    getCurrentPosition().then((position) => {
                      setCords({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                      });
                      onLocationUpdate?.();
                      onOpenChange(false);
                    });
                  }
                }}
              >
                <div className="flex items-center gap-8">
                  <LocationCurrentIcon className="size-24" fill="var(--color-content-theme)" />
                  Use your current location
                </div>
                <ChevronRight />
              </div>
              {globalData.addresses && globalData.addresses.length > 0 && (
                <div className="flex flex-col gap-12 py-14 overflow-y-scroll max-h-350">
                  {globalData.addresses.map((address) => (
                    <SavedAddressCard
                      key={address.id}
                      address={address}
                      showActions={false}
                      onClick={() => {
                        setCords({
                          latitude: address.coordinates.current_latitude,
                          longitude: address.coordinates.current_longitude,
                        });
                        onLocationUpdate?.();
                        onOpenChange(false);
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
          <div className="flex flex-col flex-1 gap-12">
            {isLoading && (
              <div className="flex-1 flex items-center justify-center">
                <Loader />
              </div>
            )}
            {searchResults && searchResults.predictions && searchResults.predictions.length > 0 && (
              <div className="flex flex-col gap-8">
                {searchResults.predictions.map((result) => (
                  <div
                    className="flex flex-col w-full px-12 py-12 rounded-md bg-surface-secondary justify-center gap-1 cursor-pointer"
                    key={result.place_id}
                    onClick={async () => {
                      const location = await getLocationByPlaceId(result.place_id);
                      setCords({
                        latitude: location.coordinates.lat,
                        longitude: location.coordinates.lng,
                      });
                      onLocationUpdate?.();
                      onOpenChange(false);
                    }}
                  >
                    <div className="flex items-center gap-5 flex-1">
                      <LocationEnabled className="size-20" fill="var(--content-secondary)" />
                      <div className="line-clamp-1 w-300">
                        {result.structured_formatting.main_text}
                      </div>
                    </div>
                    <p className="text-xs text-content-secondary">
                      {result.structured_formatting.secondary_text}
                    </p>
                  </div>
                ))}
              </div>
            )}
            {searchResults &&
              searchResults.predictions &&
              searchResults.predictions.length === 0 &&
              !isLoading && <div className="text-center text-gray-500">No results found.</div>}
          </div>
        </div>
        <LocationNotEnabledDialogue />
      </SheetContent>
    </Sheet>
  );
}
