'use client';

import CurrentLocationButton from './current-location';
import LocationEnabled from '../icons/location-enabled';
import { Skeleton } from '../ui/skeleton';
import { useUpdateDeliveryLocation } from '@/mutations/location.mutations';
import { useRouter } from 'next/navigation';
import LocationSearchSheet from './location-search-sheet';
import { useState } from 'react';
import { Search } from 'lucide-react';
import { AddressForm } from './address-form';
import { useDebouncedGetDeliveryStatus, useGetInitialMapData } from '@/queries/location.queries';
import { DeliveryStatusResponse } from '@/actions/location.actions';
import { useGlobalActionsStore } from '@/store/global-actions.store';

interface MapInterfaceProps {
  initialCords: { latitude: number; longitude: number };
  initialDeliveryStatus: DeliveryStatusResponse;
  cords?: { latitude: number; longitude: number };
  setCords: (cords: { latitude: number; longitude: number }) => void;
  action: 'edit' | 'new' | 'set';
  addressId?: string;
  onLocationUpdate?: () => void;
}

export default function MapInterface({
  initialCords,
  initialDeliveryStatus,
  cords,
  setCords,
  action,
  addressId,
  onLocationUpdate,
}: MapInterfaceProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false); // search sheet
  const [isOpenAddAddressForm, setIsOpenAddAddressForm] = useState(false);
  const { data: addressData } = useGetInitialMapData(action === 'edit' ? { addressId } : {});
  const { mutateAsync: updateDeliveryLocation, isPending } = useUpdateDeliveryLocation();

  const { data: deliveryStatus, isLoading } = useDebouncedGetDeliveryStatus(
    {
      latitude: cords?.latitude,
      longitude: cords?.longitude,
    },
    300,
    {
      enabled:
        cords?.latitude !== initialCords.latitude || cords?.longitude !== initialCords.longitude,
    }
  );

  const deliveryTime = deliveryStatus?.address
    ? deliveryStatus?.delivery_time
    : initialDeliveryStatus.delivery_time;
  const address = deliveryStatus?.address ?? initialDeliveryStatus.address;

  return (
    <>
      <div className="w-[calc(var(--scale)*377)] border-none pt-8 px-16 pb-16 bg-surface-primary absolute top-0 -left-1 z-100">
        <div
          className="w-full h-40 bg-surface-secondary rounded-md flex items-center px-12 gap-8 cursor-pointer"
          onClick={() => setIsOpen(true)}
        >
          <Search className="size-20" />
          <p className="text-content-secondary text-sm">Search for area, building, street name</p>
        </div>
      </div>
      <div className="flex flex-col items-end bottom-0 absolute -left-1 w-[calc(var(--scale)*377)] z-100">
        <CurrentLocationButton
          setCords={setCords}
          className="bg-surface-primary rounded-full mb-8 mr-16 p-8 flex items-center gap-4 text-sm"
        />
        <div className="rounded-t-lg bg-surface-primary p-16 w-full h-170">
          {isLoading ? (
            <>
              <Skeleton className="w-72 h-8" />
              <Skeleton className="h-52 w-275 flex-1 mt-12 mr-12" />
            </>
          ) : (
            <>
              {!deliveryTime ? (
                <p className="text-xs font-semibold text-red-500">Does not deliver to</p>
              ) : (
                <p className="text-xs font-semibold text-content-semantic">
                  {`Delivering in ${deliveryTime}`}
                </p>
              )}
              <div className="flex justify-between text-sm my-8 ">
                <div className="flex items-center gap-5 flex-1">
                  <LocationEnabled className="size-20" fill="var(--theme-purple-400)" />
                  <div className="font-semibold line-clamp-2 w-250">{address}</div>
                </div>
                <p className="text-content-theme cursor-pointer" onClick={() => setIsOpen(true)}>
                  Change
                </p>
              </div>
            </>
          )}
          <button
            className="bg-surface-theme w-full py-12 rounded-sm font-semibold mt-16 disabled:bg-surface-dead-1"
            disabled={isLoading || !deliveryTime || isPending}
            onClick={async () => {
              if (action === 'set') {
                await updateDeliveryLocation({
                  coordinate: {
                    latitude: cords?.latitude ?? initialCords.latitude,
                    longitude: cords?.longitude ?? initialCords.longitude,
                  },
                });
                useGlobalActionsStore.getState().locationSheet.hide();
                onLocationUpdate?.();
                router.back();
              } else {
                setIsOpenAddAddressForm(true);
              }
            }}
          >
            <p className="text-md">Confirm Location</p>
          </button>
        </div>
      </div>
      <AddressForm
        isOpen={isOpenAddAddressForm}
        onOpenChange={setIsOpenAddAddressForm}
        address={address}
        coordinates={cords ?? initialCords}
        addressData={addressData}
        onLocationUpdate={onLocationUpdate}
      />
      <LocationSearchSheet
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        setCords={setCords}
        onLocationUpdate={onLocationUpdate}
      />
    </>
  );
}
